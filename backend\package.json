{"name": "portfolio-backend", "version": "1.0.0", "description": "Backend API for portfolio contact form", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "nodemailer": "^6.9.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["portfolio", "contact", "email", "api"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}