import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Upload, X } from "lucide-react";

const FloatingResumeUpload = () => {
  const [isVisible, setIsVisible] = useState(true);

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Handle resume upload logic here
      console.log("Resume uploaded:", file.name);
      // You can add your upload logic here
    }
  };

  const triggerFileInput = () => {
    const fileInput = document.getElementById('resume-upload') as HTMLInputElement;
    fileInput?.click();
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Fixed floating resume upload icon */}
      <div className="fixed bottom-6 right-6 z-50">
        <div className="relative">
          {/* Close button */}
          <Button
            variant="outline"
            size="icon"
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600 text-white z-10"
            onClick={() => setIsVisible(false)}
          >
            <X className="h-3 w-3" />
          </Button>
          
          {/* Main upload button */}
          <Button
            className="btn-neon h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
            onClick={triggerFileInput}
          >
            <Upload className="h-6 w-6" />
          </Button>
          
          {/* Tooltip */}
          <div className="absolute bottom-16 right-0 bg-black text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            Upload Resume
            <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        id="resume-upload"
        type="file"
        accept=".pdf,.doc,.docx"
        onChange={handleResumeUpload}
        className="hidden"
      />
    </>
  );
};

export default FloatingResumeUpload;
