import Navigation from "@/components/Navigation";
import HeroSection from "@/components/HeroSection";
import AboutSection from "@/components/AboutSection";
import ResumeSection from "@/components/ResumeSection";
import ExperienceSection from "@/components/ExperienceSection";
import ProjectsSection from "@/components/ProjectsSection";
import ContactSection from "@/components/ContactSection";
import AnimatedBackground from "@/components/AnimatedBackground";
import FloatingIcons from "@/components/FloatingIcons";
import FloatingResumeUpload from "@/components/FloatingResumeUpload";
import ErrorBoundary from "@/components/ErrorBoundary";

const Index = () => {
  return (
    <div className="min-h-screen bg-background no-overflow">
      {/* Enhanced Animated Background */}
      <AnimatedBackground />

      {/* Floating Icons System */}
      <FloatingIcons />

      {/* Floating Resume Upload */}
      <FloatingResumeUpload />

      <Navigation />

      <main className="relative z-10">
        <ErrorBoundary>
          <HeroSection />
          <AboutSection />
          <ResumeSection />
          <ExperienceSection />
          <ProjectsSection />
          <ContactSection />
        </ErrorBoundary>
      </main>

      {/* Footer */}
      <footer className="bg-black py-8 relative overflow-hidden border-t-2 border-white/20">
        <div className="container mx-auto px-6 text-center">
          <div className="text-white space-y-4">
            <p className="text-lg sm:text-xl font-medium">
              Transforming data into insight, code into impact
            </p>
            <p className="text-sm sm:text-base font-medium">
              © 2025 built by Priyanshu Kumar
            </p>
          </div>
        </div>

        {/* Footer background effects */}
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="absolute top-4 left-10 w-8 h-8 bg-white rounded-full animate-pulse float"></div>
          <div className="absolute top-12 right-20 w-6 h-6 bg-white rounded-full animate-pulse float-delayed"></div>
          <div className="absolute bottom-8 left-1/3 w-4 h-4 bg-white rounded-full animate-pulse float"></div>
          <div className="absolute bottom-4 right-1/4 w-10 h-10 bg-white rounded-full animate-pulse float-delayed"></div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
